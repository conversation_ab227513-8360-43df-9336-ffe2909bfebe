import { http, HttpResponse } from 'msw';

// Mock Google Sheets API responses
export const googleSheetsHandlers = [
  // Mock successful sheet data append
  http.post('https://sheets.googleapis.com/v4/spreadsheets/:sheetId/values/:range:append', () => {
    return HttpResponse.json({
      spreadsheetId: 'test-sheet-id',
      tableRange: 'A1:AD1000',
      updates: {
        spreadsheetId: 'test-sheet-id',
        updatedRange: 'A1001:AD1001',
        updatedRows: 1,
        updatedColumns: 30,
        updatedCells: 30
      }
    });
  }),

  // Mock sheet data retrieval
  http.get('https://sheets.googleapis.com/v4/spreadsheets/:sheetId/values/:range', () => {
    return HttpResponse.json({
      range: 'A1:AD1000',
      majorDimension: 'ROWS',
      values: [
        ['姓名', '電子郵件', '電話', '場次', '參與類型', '提交時間', '付款狀態', '訂單編號'],
        ['測試用戶', '<EMAIL>', '0912345678', '台北 07/20（日）13:20', '個人報名', '2025-01-01T00:00:00.000Z', '2', 'pangea_test_123']
      ]
    });
  }),
];

// Mock PayUni API responses
export const payuniHandlers = [
  // Mock payment creation
  http.post('https://sandbox-api.payuni.com.tw/api/trade', () => {
    return HttpResponse.json({
      Status: 'SUCCESS',
      Message: '訂單建立成功',
      Result: {
        MerTradeNo: 'pangea_test_123',
        TradeNo: 'test_payuni_456',
        PaymentURL: 'https://sandbox-api.payuni.com.tw/api/payment/test_payuni_456'
      }
    });
  }),

  // Mock payment query
  http.post('https://sandbox-api.payuni.com.tw/api/trade_query', () => {
    return HttpResponse.json({
      Status: 'SUCCESS',
      Message: '查詢成功',
      'Result[0][MerTradeNo]': 'pangea_test_123',
      'Result[0][TradeNo]': 'test_payuni_456',
      'Result[0][TradeStatus]': '1',
      'Result[0][PaymentType]': '1',
      'Result[0][TradeAmt]': '3000',
      'Result[0][PaymentDay]': '2025-01-01 12:00:00'
    });
  }),
];

// Mock Meta CAPI responses
export const capiHandlers = [
  http.post('https://graph.facebook.com/v18.0/:pixelId/events', () => {
    return HttpResponse.json({
      events_received: 1,
      messages: [],
      fbtrace_id: 'test-trace-id'
    });
  }),
];

// Combine all handlers
export const handlers = [
  ...googleSheetsHandlers,
  ...payuniHandlers,
  ...capiHandlers,
];
